{"format_version": "1.8.0", "animations": {"animation.ithiefi_ditsh_vita_mimic.idle": {"loop": true, "animation_length": 3.5834, "bones": {"root": {"rotation": ["math.sin(100*q.anim_time)*2", 0, "math.cos(100*q.anim_time)*1"]}, "torso": {"rotation": ["math.sin(100*q.anim_time-40)*4", 0, "math.cos(100*q.anim_time-40)*4"]}, "rightarm": {"rotation": ["-14.0802+math.sin(100*q.anim_time-40*2)*4", 2.71036, "-2.1309+math.cos(100*q.anim_time-40*2)*4"]}, "lowerrightarm": {"rotation": ["math.sin(100*q.anim_time-40*3)*6", 0, "math.cos(100*q.anim_time-40*3)*6"]}, "righthand": {"rotation": ["math.sin(100*q.anim_time-40*4)*8", 0, "math.cos(100*q.anim_time-40*4)*8"]}, "2finger": {"rotation": ["math.sin(100*q.anim_time-40*5)*10", 0, "math.cos(100*q.anim_time-40*5)*10"]}, "1finger": {"rotation": ["math.sin(100*q.anim_time-40*6)*12", 0, "math.cos(100*q.anim_time-40*6)*12"]}, "3finger": {"rotation": ["math.sin(100*q.anim_time-40*6)*12", 0, "math.cos(100*q.anim_time-40*6)*12"]}, "leftarm": {"rotation": ["-14.0802+math.sin(100*q.anim_time-40*2)*4", -2.71036, "2.1309+math.cos(100*q.anim_time-40*2)*4"]}, "lowerleftarm": {"rotation": ["math.sin(100*q.anim_time-40*4)*8", 0, "-(math.cos(100*q.anim_time-40*4)*8)"]}, "lefthand": {"rotation": ["math.sin(100*q.anim_time-40*4)*8", 0, "math.cos(100*q.anim_time-40*4)*8"]}, "6finger": {"rotation": ["math.sin(100*q.anim_time-40*6)*12", 0, "math.cos(100*q.anim_time-40*6)*12"]}, "5finger": {"rotation": ["math.sin(100*q.anim_time-40*5)*10", 0, "-(math.cos(100*q.anim_time-40*5)*10)"]}, "4finger": {"rotation": ["math.sin(100*q.anim_time-40*6)*12", 0, "-(math.cos(100*q.anim_time-40*6)*12)"]}, "head": {"rotation": ["math.sin(100*q.anim_time-40*2)*5", 0, "math.cos(100*q.anim_time-40*2)*5"]}, "jaw": {"rotation": ["-10+math.sin(100*q.anim_time-40*3)*8", 0, 0]}, "rightLeg": {"rotation": ["-math.sin(100*q.anim_time)*2", 0, "-math.cos(100*q.anim_time)*1"]}, "rightKnee": {"rotation": ["-math.sin(100*q.anim_time-120)*2", 0, "-math.cos(100*q.anim_time-120)*1"]}, "leftLeg": {"rotation": ["math.sin(100*q.anim_time)*2", 0, "math.cos(100*q.anim_time)*1"]}, "leftKnee": {"rotation": ["math.sin(100*q.anim_time-120)*2", 0, "math.cos(100*q.anim_time-120)*1"]}}}, "animation.ithiefi_ditsh_vita_mimic.walk": {"loop": true, "animation_length": 1.7918, "bones": {"root": {"position": [0, "-math.sin(800*q.anim_time)*1", 0]}, "upperbody": {"rotation": ["math.sin(800*q.anim_time-80)*2", 0, "math.cos(400*q.anim_time-80)*2"], "position": [0, "-1+math.sin(800*q.anim_time-40)*0.4", 0]}, "rightarm": {"rotation": ["-0.2583+math.sin(400*q.anim_time-120)*24", 24.98184, -5.04139]}, "lowerrightarm": {"rotation": ["math.sin(400*q.anim_time-160)*24", 0, 0]}, "righthand": {"rotation": ["math.sin(400*q.anim_time-200)*24", 0, 0]}, "2finger": {"rotation": ["math.sin(400*q.anim_time-240)*24", 0, 0]}, "1finger": {"rotation": ["math.sin(400*q.anim_time-240)*24", 0, 0]}, "3finger": {"rotation": ["math.sin(400*q.anim_time-240)*24", 0, 0]}, "leftarm": {"rotation": ["-1.447977526-math.sin(400*q.anim_time-120)*24", -25.36764, 10.2481]}, "lowerleftarm": {"rotation": ["-math.sin(400*q.anim_time-160)*24", 0, 0]}, "lefthand": {"rotation": ["-math.sin(400*q.anim_time-200)*24", 0, 0]}, "6finger": {"rotation": ["-math.sin(400*q.anim_time-240)*24", 0, 0]}, "5finger": {"rotation": ["-math.sin(400*q.anim_time-240)*24", 0, 0]}, "4finger": {"rotation": ["-math.sin(400*q.anim_time-240)*24", 0, 0]}, "head": {"rotation": ["-math.sin(800*q.anim_time-40)*2", 0, "-math.cos(400*q.anim_time-40)*2"]}, "jaw": {"rotation": ["-12.5-math.sin(800*q.anim_time-80)*4", 0, 0]}, "rightLeg": {"rotation": ["-math.cos(400*q.anim_time)*24", 0, 0], "position": [0, "math.clamp(-math.sin(q.anim_time* 400)*2,0,8)", 0]}, "rightKnee": {"rotation": ["math.clamp(-math.sin(400*q.anim_time-20)*32,-2,24)", 0, 0]}, "leftLeg": {"rotation": ["math.cos(400*q.anim_time)*24", 0, 0], "position": [0, "math.clamp(math.sin(q.anim_time* 400)*2,0,8)", 0]}, "leftKnee": {"rotation": ["math.clamp(math.sin(400*q.anim_time-20)*32,-2,24)", 0, 0]}}}, "animation.ithiefi_ditsh_vita_mimic.attack": {"loop": "hold_on_last_frame", "animation_length": 0.5, "bones": {"torso": {"rotation": {"0.0": [2.5, 0, 0], "0.0417": [7.5, 0, 0], "0.125": [2.53, 4.96, 0.65], "0.1667": [0.03, 4.96, 0.65], "0.25": [21.01, -8.45, 0.48], "0.2917": [28.92, -26.24, -9.15], "0.375": [13.92, -26.24, -9.15], "0.4167": [6.79, -10.25, 0.01], "0.4583": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.2917": [0, 0, 0], "0.375": [0, 0, 0], "0.4167": [0, 0, 0], "0.4583": [0, 0, 0]}}, "rightarm": {"rotation": {"0.0": [-42.19, 17.39, 18.25], "0.0417": [-77.38, -12.09, -1.83], "0.125": [-164.17, -7.21, 15.72], "0.1667": [-211.01, 13.22, 33.19], "0.25": [-48.19, -2.32, 61.96], "0.2917": [-38.19, -2.32, 61.96], "0.375": [-42.19, -17.39, -18.25]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, -3], "0.2917": [0, 0, -1.5], "0.375": [0, 0, 0]}}, "leftarm": {"rotation": {"0.0": [-42.19, 17.39, 18.25], "0.0417": [-77.38, 12.09, 1.83], "0.125": [-164.17, 7.21, -15.72], "0.1667": [-211.01, -13.22, -33.19], "0.25": [-48.19, 2.32, -61.96], "0.2917": [-38.19, 2.32, -61.96], "0.375": [-42.19, 17.39, 18.25]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, -3], "0.2917": [0, 0, -1.5], "0.375": [0, 0, 0]}}, "head": {"rotation": {"0.0": [2.5, 0, 0], "0.0417": [2.5, 0, 0], "0.125": [10.01, -5, -0.22], "0.1667": [10.01, -5, -0.22], "0.25": [-13.1, 7.73, -0.57], "0.2917": [-13.18, 25.33, -2.19], "0.375": [4.11, 27.32, 4.11], "0.4167": [2.83, 10.15, -2.21], "0.4583": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.2917": [0, 0, 0], "0.375": [0, 0, 0], "0.4167": [0, 0, 0], "0.4583": [0, 0, 0]}}, "jaw": {"rotation": {"0.0": [-30, 0, 0], "0.1667": [27.5, 0, 0], "0.25": [-30, 0, 0], "0.375": [0, 0, 0]}}}}}}