import { Entity, GameMode, Player, Vector3 } from "@minecraft/server";
import { getDistance, getRandomLocation } from "../utilities/vector3";

const TELEPORT_OFFSET_DISTANCE = 4;

/**
 * Handles the teleportation behavior for <PERSON><PERSON><PERSON> when the teleport event is triggered.
 * This function is called when the randomize function in the entity JSON determines
 * that teleportation should occur. Teleports the stalker immediately to the nearest player.
 *
 * @param stalker - The Stalker entity
 */
export function stalkerTeleportHandler(stalker: Entity): void {
  try {

    const location: Vector3 = stalker.location;

    // Get all valid players in a large radius
    const players: Player[] = stalker.dimension.getPlayers({
      location: location,
      maxDistance: 256,
      excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });

    if (players.length === 0) {
      return;
    }

    // Find the nearest player
    let nearestPlayer: Player = players[0]!;
    let nearestDistance: number = getDistance(location, nearestPlayer.location);

    for (const player of players) {
      const distance: number = getDistance(location, player.location);
      if (distance < nearestDistance) {
        nearestDistance = distance;
        nearestPlayer = player;
      }
    }

    // Try multiple teleportation strategies with retry system
    let finalTeleportLocation: Vector3 | undefined;

    // Strategy 1: Try teleporting behind the player (based on view direction)
    const playerViewDirection: Vector3 = nearestPlayer.getViewDirection();
    const behindPlayerLocation: Vector3 = {
      x: nearestPlayer.location.x - (playerViewDirection.x * TELEPORT_OFFSET_DISTANCE),
      y: nearestPlayer.location.y,
      z: nearestPlayer.location.z - (playerViewDirection.z * TELEPORT_OFFSET_DISTANCE)
    };

    const behindPlayerBlock = stalker.dimension.getBlock(behindPlayerLocation);
    if (behindPlayerBlock?.isAir) {
      finalTeleportLocation = behindPlayerLocation;
    }

    // Strategy 2: Try teleporting to the side of the player
    if (!finalTeleportLocation) {
      const sidePlayerLocation: Vector3 = {
        x: nearestPlayer.location.x + (playerViewDirection.z * TELEPORT_OFFSET_DISTANCE), // Perpendicular to view direction
        y: nearestPlayer.location.y,
        z: nearestPlayer.location.z - (playerViewDirection.x * TELEPORT_OFFSET_DISTANCE)
      };

      const sidePlayerBlock = stalker.dimension.getBlock(sidePlayerLocation);
      if (sidePlayerBlock?.isAir) {
        finalTeleportLocation = sidePlayerLocation;
      }
    }

    // Strategy 3: Try random locations around the player
    if (!finalTeleportLocation) {
      finalTeleportLocation = getRandomLocation(
        nearestPlayer.location,
        stalker.dimension,
        2, // baseOffset: 2 blocks minimum distance
        4, // additionalOffset: up to 6 blocks total distance (2+4)
        0, // randomYOffset: same Y level as player
        true // checkForAirBlock: ensure safe teleportation
      );
    }

    // Strategy 4: If random locations failed, try closer to player with smaller range
    if (!finalTeleportLocation) {
      finalTeleportLocation = getRandomLocation(
        nearestPlayer.location,
        stalker.dimension,
        1, // baseOffset: 1 block minimum distance
        2, // additionalOffset: up to 3 blocks total distance (1+2)
        0, // randomYOffset: same Y level as player
        true // checkForAirBlock: ensure safe teleportation
      );
    }

    // Strategy 5: Final fallback - teleport directly to player location
    if (!finalTeleportLocation) {
      finalTeleportLocation = {
        x: nearestPlayer.location.x,
        y: nearestPlayer.location.y,
        z: nearestPlayer.location.z
      };
    }

    // Teleport Stalker
    stalker.teleport(finalTeleportLocation);

    // Play teleport sound effect
    stalker.dimension.playSound("mob.ditsh.stalker.teleport", finalTeleportLocation);

  } catch (error) {
    console.warn(`Failed to teleport Stalker: ${error}`);
  }
  return;
}
